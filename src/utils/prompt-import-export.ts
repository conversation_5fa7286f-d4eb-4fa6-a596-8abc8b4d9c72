import { Prompt, ExportData, ImportResult, EXPORT_VERSION } from "@/types/prompts";

/**
 * Export prompts to JSON format
 */
export function exportPrompts(prompts: Prompt[]): string {
  const exportData: ExportData = {
    version: EXPORT_VERSION,
    exportedAt: new Date().toISOString(),
    prompts: prompts,
  };

  return JSON.stringify(exportData, null, 2);
}

/**
 * Download prompts as a JSON file
 */
export function downloadPromptsAsFile(prompts: Prompt[], filename?: string): void {
  const jsonData = exportPrompts(prompts);
  const blob = new Blob([jsonData], { type: "application/json" });
  const url = URL.createObjectURL(blob);

  const link = document.createElement("a");
  link.href = url;
  link.download = filename || `prompts-export-${new Date().toISOString().split('T')[0]}.json`;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);

  URL.revokeObjectURL(url);
}

/**
 * Parse and validate imported prompt data
 */
export function parseImportData(jsonString: string): { success: boolean; data?: ExportData; error?: string } {
  try {
    const data = JSON.parse(jsonString);

    // Basic structure validation
    if (!data || typeof data !== "object") {
      return { success: false, error: "Invalid JSON structure" };
    }

    if (!data.version) {
      return { success: false, error: "Missing version information" };
    }

    if (!Array.isArray(data.prompts)) {
      return { success: false, error: "Invalid prompts data" };
    }

    // Validate each prompt
    for (const prompt of data.prompts) {
      if (!prompt.id || !prompt.name || !prompt.content) {
        return { success: false, error: "Invalid prompt structure" };
      }
      if (typeof prompt.name !== "string" || typeof prompt.content !== "string") {
        return { success: false, error: "Invalid prompt data types" };
      }
    }

    return { success: true, data };
  } catch (error) {
    return { success: false, error: "Invalid JSON format" };
  }
}

/**
 * Process imported prompts and merge with existing ones
 */
export function processImportedPrompts(
  importedPrompts: Prompt[],
  existingPrompts: Prompt[],
  options: {
    skipDuplicates?: boolean;
    overwriteExisting?: boolean;
  } = {}
): ImportResult {
  const { skipDuplicates = true, overwriteExisting = false } = options;
  const result: ImportResult = {
    success: true,
    imported: 0,
    skipped: 0,
    errors: [],
  };

  const existingNames = new Set(existingPrompts.map(p => p.name.toLowerCase()));
  const existingIds = new Set(existingPrompts.map(p => p.id));

  for (const importedPrompt of importedPrompts) {
    try {
      // Check for duplicate names
      if (existingNames.has(importedPrompt.name.toLowerCase())) {
        if (skipDuplicates && !overwriteExisting) {
          result.skipped++;
          continue;
        }
      }

      // Check for duplicate IDs and generate new ones if needed
      if (existingIds.has(importedPrompt.id)) {
        importedPrompt.id = generateUniqueId(existingIds);
      }

      // Validate prompt content length
      if (importedPrompt.content.length > 10000) {
        result.errors.push(`Prompt "${importedPrompt.name}" exceeds maximum length`);
        continue;
      }

      if (importedPrompt.content.trim().length < 10) {
        result.errors.push(`Prompt "${importedPrompt.name}" is too short`);
        continue;
      }

      // Update timestamps
      importedPrompt.createdAt = new Date().toISOString();
      importedPrompt.updatedAt = new Date().toISOString();
      
      // Ensure no prompt is marked as default during import
      importedPrompt.isDefault = false;

      result.imported++;
      existingNames.add(importedPrompt.name.toLowerCase());
      existingIds.add(importedPrompt.id);
    } catch (error) {
      result.errors.push(`Error processing prompt "${importedPrompt.name}": ${error}`);
    }
  }

  return result;
}

/**
 * Generate a unique ID that doesn't conflict with existing IDs
 */
function generateUniqueId(existingIds: Set<string>): string {
  let id: string;
  do {
    id = Date.now().toString(36) + Math.random().toString(36).substring(2);
  } while (existingIds.has(id));
  return id;
}

/**
 * Create a file input element for importing prompts
 */
export function createFileInput(
  onFileSelect: (file: File) => void,
  accept: string = ".json"
): HTMLInputElement {
  const input = document.createElement("input");
  input.type = "file";
  input.accept = accept;
  input.style.display = "none";

  input.addEventListener("change", (event) => {
    const file = (event.target as HTMLInputElement).files?.[0];
    if (file) {
      onFileSelect(file);
    }
  });

  return input;
}

/**
 * Read file content as text
 */
export function readFileAsText(file: File): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = (event) => {
      const result = event.target?.result;
      if (typeof result === "string") {
        resolve(result);
      } else {
        reject(new Error("Failed to read file as text"));
      }
    };
    reader.onerror = () => reject(new Error("Error reading file"));
    reader.readAsText(file);
  });
}

/**
 * Complete import workflow: select file, parse, and validate
 */
export async function importPromptsFromFile(): Promise<{
  success: boolean;
  prompts?: Prompt[];
  error?: string;
}> {
  return new Promise((resolve) => {
    const input = createFileInput(async (file) => {
      try {
        const content = await readFileAsText(file);
        const parseResult = parseImportData(content);
        
        if (!parseResult.success) {
          resolve({ success: false, error: parseResult.error });
          return;
        }

        resolve({ 
          success: true, 
          prompts: parseResult.data!.prompts 
        });
      } catch (error) {
        resolve({ 
          success: false, 
          error: `Error reading file: ${error}` 
        });
      }
    });

    document.body.appendChild(input);
    input.click();
    document.body.removeChild(input);
  });
}
