import { NextResponse } from "next/server";
import { DEFAULT_INTERVIEW_PROMPT } from "@/constants/prompts";

// Types for prompt management
interface Prompt {
  id: string;
  name: string;
  content: string;
  isDefault: boolean;
  createdAt: string;
  updatedAt: string;
}

// In-memory storage for simplicity (you could use filesystem or database)
const storedPrompts: Prompt[] = [
  {
    id: "default",
    name: "Default Interview Prompt",
    content: DEFAULT_INTERVIEW_PROMPT,
    isDefault: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
];

// Helper function to get the active/default prompt
function getActivePrompt(): Prompt {
  return storedPrompts.find((p) => p.isDefault) || storedPrompts[0];
}

// Constants for validation
const MAX_PROMPT_LENGTH = 10000; // 10KB limit
const MIN_PROMPT_LENGTH = 10;

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const action = searchParams.get("action");

    if (action === "list") {
      // Return all prompts
      return NextResponse.json({ prompts: storedPrompts });
    } else {
      // Return active/default prompt for backward compatibility
      const activePrompt = getActivePrompt();
      return NextResponse.json({ prompt: activePrompt.content });
    }
  } catch (error) {
    console.error("Error retrieving prompt:", error);
    return NextResponse.json(
      { error: "Failed to retrieve prompt" },
      { status: 500 }
    );
  }
}

export async function POST(request: Request) {
  try {
    // Validate Content-Type
    const contentType = request.headers.get("content-type");
    if (!contentType || !contentType.includes("application/json")) {
      return NextResponse.json(
        { error: "Content-Type must be application/json" },
        { status: 400 }
      );
    }

    // Parse and validate request body
    let body;
    try {
      body = await request.json();
    } catch (parseError) {
      console.error("JSON parsing error:", parseError);
      return NextResponse.json(
        { error: "Invalid JSON in request body" },
        { status: 400 }
      );
    }

    const { action, prompt, id, name } = body;

    // Handle different actions
    if (action === "create") {
      return handleCreatePrompt(body);
    } else if (action === "update") {
      return handleUpdatePrompt(body);
    } else if (action === "delete") {
      return handleDeletePrompt(body);
    } else if (action === "setDefault") {
      return handleSetDefaultPrompt(body);
    } else {
      // Legacy behavior - update the default prompt content
      return handleLegacyUpdate(body);
    }
  } catch (error) {
    console.error("Error in POST handler:", error);
    return NextResponse.json(
      { error: "Internal server error. Please try again later." },
      { status: 500 }
    );
  }
}

// Legacy update function for backward compatibility
async function handleLegacyUpdate(body: any) {
  // Validate prompt field exists
  if (!body.hasOwnProperty("prompt")) {
    return NextResponse.json(
      { error: 'Missing required field "prompt"' },
      { status: 400 }
    );
  }

  const { prompt } = body;

  // Validate prompt type
  if (typeof prompt !== "string") {
    return NextResponse.json(
      { error: "Prompt must be a string" },
      { status: 400 }
    );
  }

  // Validate prompt content (basic sanitization) - check this first
  const trimmedPrompt = prompt.trim();
  if (!trimmedPrompt) {
    return NextResponse.json(
      { error: "Prompt cannot be empty or contain only whitespace" },
      { status: 400 }
    );
  }

  // Validate prompt length (use trimmed length)
  if (trimmedPrompt.length < MIN_PROMPT_LENGTH) {
    return NextResponse.json(
      {
        error: `Prompt must be at least ${MIN_PROMPT_LENGTH} characters long`,
      },
      { status: 400 }
    );
  }

  if (trimmedPrompt.length > MAX_PROMPT_LENGTH) {
    return NextResponse.json(
      { error: `Prompt must not exceed ${MAX_PROMPT_LENGTH} characters` },
      { status: 400 }
    );
  }

  // Update the default prompt content
  const activePrompt = getActivePrompt();
  activePrompt.content = trimmedPrompt;
  activePrompt.updatedAt = new Date().toISOString();

  console.log(
    `Prompt updated successfully. Length: ${trimmedPrompt.length} characters`
  );

  return NextResponse.json({
    success: true,
    message: "Prompt saved successfully",
    promptLength: trimmedPrompt.length,
  });
}

// Helper function to validate prompt content
function validatePromptContent(content: string) {
  if (typeof content !== "string") {
    return { valid: false, error: "Prompt must be a string" };
  }

  const trimmedContent = content.trim();
  if (!trimmedContent) {
    return {
      valid: false,
      error: "Prompt cannot be empty or contain only whitespace",
    };
  }

  if (trimmedContent.length < MIN_PROMPT_LENGTH) {
    return {
      valid: false,
      error: `Prompt must be at least ${MIN_PROMPT_LENGTH} characters long`,
    };
  }

  if (trimmedContent.length > MAX_PROMPT_LENGTH) {
    return {
      valid: false,
      error: `Prompt must not exceed ${MAX_PROMPT_LENGTH} characters`,
    };
  }

  return { valid: true, content: trimmedContent };
}

// Generate unique ID for new prompts
function generateId(): string {
  return Date.now().toString(36) + Math.random().toString(36).substring(2);
}

// Create new prompt
async function handleCreatePrompt(body: any) {
  const { name, content } = body;

  if (!name || typeof name !== "string" || !name.trim()) {
    return NextResponse.json(
      { error: "Prompt name is required" },
      { status: 400 }
    );
  }

  if (!content) {
    return NextResponse.json(
      { error: "Prompt content is required" },
      { status: 400 }
    );
  }

  const validation = validatePromptContent(content);
  if (!validation.valid) {
    return NextResponse.json({ error: validation.error }, { status: 400 });
  }

  // Check if name already exists
  if (
    storedPrompts.some(
      (p) => p.name.toLowerCase() === name.trim().toLowerCase()
    )
  ) {
    return NextResponse.json(
      { error: "A prompt with this name already exists" },
      { status: 400 }
    );
  }

  const newPrompt: Prompt = {
    id: generateId(),
    name: name.trim(),
    content: validation.content!,
    isDefault: storedPrompts.length === 0, // First prompt becomes default
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  };

  storedPrompts.push(newPrompt);

  return NextResponse.json({
    success: true,
    message: "Prompt created successfully",
    prompt: newPrompt,
  });
}

// Update existing prompt
async function handleUpdatePrompt(body: any) {
  const { id, name, content } = body;

  if (!id) {
    return NextResponse.json(
      { error: "Prompt ID is required" },
      { status: 400 }
    );
  }

  const promptIndex = storedPrompts.findIndex((p) => p.id === id);
  if (promptIndex === -1) {
    return NextResponse.json({ error: "Prompt not found" }, { status: 404 });
  }

  const prompt = storedPrompts[promptIndex];

  // Update name if provided
  if (name !== undefined) {
    if (!name || typeof name !== "string" || !name.trim()) {
      return NextResponse.json(
        { error: "Prompt name cannot be empty" },
        { status: 400 }
      );
    }

    // Check if name already exists (excluding current prompt)
    if (
      storedPrompts.some(
        (p) => p.id !== id && p.name.toLowerCase() === name.trim().toLowerCase()
      )
    ) {
      return NextResponse.json(
        { error: "A prompt with this name already exists" },
        { status: 400 }
      );
    }

    prompt.name = name.trim();
  }

  // Update content if provided
  if (content !== undefined) {
    const validation = validatePromptContent(content);
    if (!validation.valid) {
      return NextResponse.json({ error: validation.error }, { status: 400 });
    }
    prompt.content = validation.content!;
  }

  prompt.updatedAt = new Date().toISOString();

  return NextResponse.json({
    success: true,
    message: "Prompt updated successfully",
    prompt: prompt,
  });
}

// Delete prompt
async function handleDeletePrompt(body: any) {
  const { id } = body;

  if (!id) {
    return NextResponse.json(
      { error: "Prompt ID is required" },
      { status: 400 }
    );
  }

  const promptIndex = storedPrompts.findIndex((p) => p.id === id);
  if (promptIndex === -1) {
    return NextResponse.json({ error: "Prompt not found" }, { status: 404 });
  }

  const prompt = storedPrompts[promptIndex];

  // Don't allow deleting the last prompt
  if (storedPrompts.length === 1) {
    return NextResponse.json(
      { error: "Cannot delete the last remaining prompt" },
      { status: 400 }
    );
  }

  // If deleting the default prompt, make another one default
  if (prompt.isDefault && storedPrompts.length > 1) {
    const nextPrompt = storedPrompts.find((p) => p.id !== id);
    if (nextPrompt) {
      nextPrompt.isDefault = true;
    }
  }

  storedPrompts.splice(promptIndex, 1);

  return NextResponse.json({
    success: true,
    message: "Prompt deleted successfully",
  });
}

// Set default prompt
async function handleSetDefaultPrompt(body: any) {
  const { id } = body;

  if (!id) {
    return NextResponse.json(
      { error: "Prompt ID is required" },
      { status: 400 }
    );
  }

  const prompt = storedPrompts.find((p) => p.id === id);
  if (!prompt) {
    return NextResponse.json({ error: "Prompt not found" }, { status: 404 });
  }

  // Remove default from all prompts
  storedPrompts.forEach((p) => (p.isDefault = false));

  // Set new default
  prompt.isDefault = true;

  return NextResponse.json({
    success: true,
    message: "Default prompt updated successfully",
    prompt: prompt,
  });
}
