// Types for prompt management system

export interface Prompt {
  id: string;
  name: string;
  content: string;
  isDefault: boolean;
  createdAt: string;
  updatedAt: string;
}

// API request types
export interface CreatePromptRequest {
  action: 'create';
  name: string;
  content: string;
}

export interface UpdatePromptRequest {
  action: 'update';
  id: string;
  name?: string;
  content?: string;
}

export interface DeletePromptRequest {
  action: 'delete';
  id: string;
}

export interface SetDefaultPromptRequest {
  action: 'setDefault';
  id: string;
}

// Legacy request type for backward compatibility
export interface LegacyUpdateRequest {
  prompt: string;
}

// API response types
export interface ApiResponse<T = any> {
  success: boolean;
  error?: string;
  data?: T;
}

export interface GetPromptsResponse {
  prompts: Prompt[];
}

export interface GetActivePromptResponse {
  prompt: string;
}

export interface CreatePromptResponse {
  success: true;
  message: string;
  prompt: Prompt;
}

export interface UpdatePromptResponse {
  success: true;
  message: string;
  prompt: Prompt;
}

export interface DeletePromptResponse {
  success: true;
  message: string;
}

export interface SetDefaultPromptResponse {
  success: true;
  message: string;
  prompt: Prompt;
}

// Legacy response type
export interface LegacyUpdateResponse {
  success: true;
  message: string;
  promptLength: number;
}

// Union types for all possible requests and responses
export type PromptRequest = 
  | CreatePromptRequest 
  | UpdatePromptRequest 
  | DeletePromptRequest 
  | SetDefaultPromptRequest 
  | LegacyUpdateRequest;

export type PromptResponse = 
  | CreatePromptResponse 
  | UpdatePromptResponse 
  | DeletePromptResponse 
  | SetDefaultPromptResponse 
  | LegacyUpdateResponse;

// UI state types
export interface PromptFormData {
  name: string;
  content: string;
}

export interface PromptValidation {
  nameValid: boolean;
  contentValid: boolean;
  nameError?: string;
  contentError?: string;
  canSave: boolean;
}

// Import/Export types
export interface ExportData {
  version: string;
  exportedAt: string;
  prompts: Prompt[];
}

export interface ImportResult {
  success: boolean;
  imported: number;
  skipped: number;
  errors: string[];
}

// Search and filter types
export interface PromptFilters {
  searchTerm: string;
  sortBy: 'name' | 'createdAt' | 'updatedAt';
  sortOrder: 'asc' | 'desc';
}

// Constants
export const PROMPT_VALIDATION = {
  MIN_NAME_LENGTH: 1,
  MAX_NAME_LENGTH: 100,
  MIN_CONTENT_LENGTH: 10,
  MAX_CONTENT_LENGTH: 10000,
} as const;

export const EXPORT_VERSION = '1.0' as const;
