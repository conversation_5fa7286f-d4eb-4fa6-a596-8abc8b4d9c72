"use client";

import { useState, useEffect } from "react";
import { makeApiCall } from "@/utils/api-helpers";
import { Prompt } from "@/types/prompts";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import {
  Too<PERSON><PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { Badge } from "@/components/ui/badge";
import { ChevronDown, Settings, Plus } from "lucide-react";
import PromptManagementModal from "./PromptManagementModal";
import SimplePromptModal from "./SimplePromptModal";

interface PromptSelectorProps {
  selectedPrompt: Prompt | null;
  onPromptSelect: (prompt: Prompt) => void;
  onPromptChange?: () => void; // Called when prompts are modified
}

export default function PromptSelector({
  selectedPrompt,
  onPromptSelect,
  onPromptChange,
}: PromptSelectorProps) {
  const [prompts, setPrompts] = useState<Prompt[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [error, setError] = useState<string>("");

  // Load prompts
  const loadPrompts = async () => {
    setIsLoading(true);
    setError("");

    const result = await makeApiCall<{ prompts: Prompt[] }>(
      "/api/questionnaire-prompt-builder?action=list"
    );

    if (result.success && result.data) {
      setPrompts(result.data.prompts);

      // If no prompt is selected, select the default one
      if (!selectedPrompt && result.data.prompts.length > 0) {
        const defaultPrompt =
          result.data.prompts.find((p) => p.isDefault) ||
          result.data.prompts[0];
        onPromptSelect(defaultPrompt);
      }
    } else {
      setError(result.error || "Failed to load prompts");
    }

    setIsLoading(false);
  };

  useEffect(() => {
    loadPrompts();
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  const handlePromptSelect = (prompt: Prompt) => {
    onPromptSelect(prompt);
    setIsDropdownOpen(false);
  };

  const handlePromptManagementChange = () => {
    loadPrompts();
    onPromptChange?.();
  };

  if (isLoading) {
    return (
      <div className="flex items-center gap-2">
        <div className="text-sm text-gray-500">Loading prompts...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center gap-2">
        <div className="text-sm text-red-600">Error: {error}</div>
        <Button variant="outline" size="sm" onClick={loadPrompts}>
          Retry
        </Button>
      </div>
    );
  }

  return (
    <TooltipProvider>
      <div className="flex items-center gap-2">
        {/* Current Prompt Display */}
        <div className="flex items-center gap-2">
          <Label className="text-sm font-medium">Active Prompt:</Label>
          <div className="flex items-center gap-2">
            <span className="text-sm font-medium">
              {selectedPrompt?.name || "No prompt selected"}
            </span>
            {selectedPrompt?.isDefault && (
              <Badge variant="secondary" className="text-xs">
                Default
              </Badge>
            )}
          </div>
        </div>

        {/* Prompt Selector Dropdown */}
        <div className="relative">
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setIsDropdownOpen(!isDropdownOpen)}
                className="flex items-center gap-1"
              >
                <ChevronDown className="h-4 w-4" />
                Switch
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>Switch to a different prompt</p>
            </TooltipContent>
          </Tooltip>

          {isDropdownOpen && (
            <div className="absolute top-full left-0 mt-1 w-80 bg-white border border-gray-200 rounded-md shadow-lg z-50">
              <div className="p-2 border-b border-gray-100">
                <div className="text-sm font-medium text-gray-700">
                  Select Prompt
                </div>
              </div>
              <div className="max-h-60 overflow-y-auto">
                {prompts.map((prompt) => (
                  <button
                    key={prompt.id}
                    onClick={() => handlePromptSelect(prompt)}
                    className={`w-full text-left p-3 hover:bg-gray-50 border-b border-gray-50 last:border-b-0 ${
                      selectedPrompt?.id === prompt.id ? "bg-blue-50" : ""
                    }`}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2 mb-1">
                          <span className="font-medium text-sm truncate">
                            {prompt.name}
                          </span>
                          {prompt.isDefault && (
                            <Badge variant="secondary" className="text-xs">
                              Default
                            </Badge>
                          )}
                        </div>
                        <p className="text-xs text-gray-600 line-clamp-2">
                          {prompt.content.substring(0, 100)}
                          {prompt.content.length > 100 ? "..." : ""}
                        </p>
                        <div className="flex items-center gap-2 mt-1 text-xs text-gray-500">
                          <span>{prompt.content.length} chars</span>
                          <span>•</span>
                          <span>
                            Updated{" "}
                            {new Date(prompt.updatedAt).toLocaleDateString()}
                          </span>
                        </div>
                      </div>
                    </div>
                  </button>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Manage Prompts Button */}
        <SimplePromptModal>
          <Button variant="outline" size="sm" title="Manage your prompts">
            <Settings className="h-4 w-4 mr-1" />
            Manage
          </Button>
        </SimplePromptModal>

        {/* Quick Create Button */}
        <PromptManagementModal
          trigger={
            <Button variant="outline" size="sm" title="Create new prompt">
              <Plus className="h-4 w-4" />
            </Button>
          }
          onPromptSelect={(prompt) => {
            handlePromptSelect(prompt);
            handlePromptManagementChange();
          }}
        />
      </div>

      {/* Click outside to close dropdown */}
      {isDropdownOpen && (
        <div
          className="fixed inset-0 z-40"
          onClick={() => setIsDropdownOpen(false)}
        />
      )}
    </TooltipProvider>
  );
}
