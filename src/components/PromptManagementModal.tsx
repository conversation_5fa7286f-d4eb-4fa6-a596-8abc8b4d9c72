"use client";

import { useState, useEffect } from "react";
import { makeApiCall, validation } from "@/utils/api-helpers";
import { Prompt, PromptFormData } from "@/types/prompts";
import {
  downloadPromptsAsFile,
  importPromptsFromFile,
  processImportedPrompts,
} from "@/utils/prompt-import-export";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Card, CardContent } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { Badge } from "@/components/ui/badge";
import {
  Search,
  Plus,
  Edit,
  Trash2,
  Star,
  StarOff,
  Download,
  Upload,
  X,
} from "lucide-react";

interface PromptManagementModalProps {
  trigger: React.ReactNode;
  onPromptSelect?: (prompt: Prompt) => void;
}

export default function PromptManagementModal({
  trigger,
  onPromptSelect,
}: PromptManagementModalProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [prompts, setPrompts] = useState<Prompt[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [editingPrompt, setEditingPrompt] = useState<Prompt | null>(null);
  const [isCreating, setIsCreating] = useState(false);
  const [formData, setFormData] = useState<PromptFormData>({
    name: "",
    content: "",
  });
  const [isSaving, setIsSaving] = useState(false);
  const [error, setError] = useState<string>("");

  // Load prompts when modal opens
  useEffect(() => {
    if (isOpen) {
      loadPrompts();
    }
  }, [isOpen]);

  const loadPrompts = async () => {
    setIsLoading(true);
    setError("");

    const result = await makeApiCall<{ prompts: Prompt[] }>(
      "/api/questionnaire-prompt-builder?action=list"
    );

    if (result.success && result.data) {
      setPrompts(result.data.prompts);
    } else {
      setError(result.error || "Failed to load prompts");
    }

    setIsLoading(false);
  };

  const handleCreate = () => {
    setIsCreating(true);
    setEditingPrompt(null);
    setFormData({ name: "", content: "" });
    setError("");
  };

  const handleEdit = (prompt: Prompt) => {
    setEditingPrompt(prompt);
    setIsCreating(false);
    setFormData({ name: prompt.name, content: prompt.content });
    setError("");
  };

  const handleSave = async () => {
    setIsSaving(true);
    setError("");

    const existingNames = prompts
      .filter((p) => p.id !== editingPrompt?.id)
      .map((p) => p.name);

    const validation_result = validation.promptForm(
      formData,
      existingNames,
      editingPrompt?.name
    );

    if (!validation_result.canSave) {
      setError(
        validation_result.nameError ||
          validation_result.contentError ||
          "Invalid form data"
      );
      setIsSaving(false);
      return;
    }

    const requestData = isCreating
      ? { action: "create", name: formData.name, content: formData.content }
      : {
          action: "update",
          id: editingPrompt!.id,
          name: formData.name,
          content: formData.content,
        };

    const result = await makeApiCall("/api/questionnaire-prompt-builder", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify(requestData),
    });

    if (result.success) {
      await loadPrompts();
      setIsCreating(false);
      setEditingPrompt(null);
      setFormData({ name: "", content: "" });
    } else {
      setError(result.error || "Failed to save prompt");
    }

    setIsSaving(false);
  };

  const handleDelete = async (prompt: Prompt) => {
    if (!confirm(`Are you sure you want to delete "${prompt.name}"?`)) {
      return;
    }

    const result = await makeApiCall("/api/questionnaire-prompt-builder", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({ action: "delete", id: prompt.id }),
    });

    if (result.success) {
      await loadPrompts();
    } else {
      setError(result.error || "Failed to delete prompt");
    }
  };

  const handleSetDefault = async (prompt: Prompt) => {
    const result = await makeApiCall("/api/questionnaire-prompt-builder", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({ action: "setDefault", id: prompt.id }),
    });

    if (result.success) {
      await loadPrompts();
    } else {
      setError(result.error || "Failed to set default prompt");
    }
  };

  const handleCancel = () => {
    setIsCreating(false);
    setEditingPrompt(null);
    setFormData({ name: "", content: "" });
    setError("");
  };

  const handleExport = () => {
    try {
      downloadPromptsAsFile(prompts);
    } catch {
      setError("Failed to export prompts");
    }
  };

  const handleImport = async () => {
    try {
      const importResult = await importPromptsFromFile();

      if (!importResult.success) {
        setError(importResult.error || "Failed to import prompts");
        return;
      }

      if (!importResult.prompts || importResult.prompts.length === 0) {
        setError("No valid prompts found in the file");
        return;
      }

      // Process the imported prompts
      const processResult = processImportedPrompts(
        importResult.prompts,
        prompts
      );

      if (processResult.errors.length > 0) {
        setError(
          `Import completed with errors: ${processResult.errors.join(", ")}`
        );
      }

      // Create the imported prompts via API
      let successCount = 0;
      for (const prompt of importResult.prompts) {
        const result = await makeApiCall("/api/questionnaire-prompt-builder", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            action: "create",
            name: prompt.name,
            content: prompt.content,
          }),
        });

        if (result.success) {
          successCount++;
        }
      }

      if (successCount > 0) {
        await loadPrompts();
        setError("");
        // Show success message
        console.log(`Successfully imported ${successCount} prompts`);
      } else {
        setError("Failed to import any prompts");
      }
    } catch {
      setError("Error during import process");
    }
  };

  const filteredPrompts = prompts.filter(
    (prompt) =>
      prompt.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      prompt.content.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const existingNames = prompts
    .filter((p) => p.id !== editingPrompt?.id)
    .map((p) => p.name);

  const formValidation = validation.promptForm(
    formData,
    existingNames,
    editingPrompt?.name
  );

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>{trigger}</DialogTrigger>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-hidden flex flex-col">
        <DialogHeader>
          <DialogTitle>Manage Prompts</DialogTitle>
        </DialogHeader>

        <div className="flex-1 overflow-hidden flex flex-col space-y-4">
          {/* Search and Actions */}
          <div className="flex items-center justify-between gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="Search prompts..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <div className="flex gap-2">
              <Button onClick={handleImport} size="sm" variant="outline">
                <Upload className="h-4 w-4 mr-2" />
                Import
              </Button>
              <Button onClick={handleExport} size="sm" variant="outline">
                <Download className="h-4 w-4 mr-2" />
                Export
              </Button>
              <Button onClick={handleCreate} size="sm">
                <Plus className="h-4 w-4 mr-2" />
                New Prompt
              </Button>
            </div>
          </div>

          {error && (
            <Alert variant="destructive">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {/* Content Area */}
          <div className="flex-1 overflow-hidden">
            {isCreating || editingPrompt ? (
              <PromptForm
                formData={formData}
                setFormData={setFormData}
                validation={formValidation}
                isSaving={isSaving}
                onSave={handleSave}
                onCancel={handleCancel}
                isCreating={isCreating}
              />
            ) : (
              <PromptList
                prompts={filteredPrompts}
                isLoading={isLoading}
                onEdit={handleEdit}
                onDelete={handleDelete}
                onSetDefault={handleSetDefault}
                onSelect={onPromptSelect}
              />
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}

// Form component for creating/editing prompts
interface PromptFormProps {
  formData: PromptFormData;
  setFormData: (data: PromptFormData) => void;
  validation: ReturnType<typeof validation.promptForm>;
  isSaving: boolean;
  onSave: () => void;
  onCancel: () => void;
  isCreating: boolean;
}

function PromptForm({
  formData,
  setFormData,
  validation: formValidation,
  isSaving,
  onSave,
  onCancel,
  isCreating,
}: PromptFormProps) {
  return (
    <div className="space-y-4 h-full flex flex-col">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold">
          {isCreating ? "Create New Prompt" : "Edit Prompt"}
        </h3>
        <Button variant="ghost" size="sm" onClick={onCancel}>
          <X className="h-4 w-4" />
        </Button>
      </div>

      <div className="space-y-4 flex-1">
        <div>
          <Label htmlFor="prompt-name">Name</Label>
          <Input
            id="prompt-name"
            value={formData.name}
            onChange={(e) => setFormData({ ...formData, name: e.target.value })}
            placeholder="Enter prompt name..."
            className={
              !formValidation.nameValid && formData.name ? "border-red-500" : ""
            }
          />
          {formValidation.nameError && (
            <p className="text-sm text-red-600 mt-1">
              {formValidation.nameError}
            </p>
          )}
        </div>

        <div className="flex-1 flex flex-col">
          <Label htmlFor="prompt-content">Content</Label>
          <Textarea
            id="prompt-content"
            value={formData.content}
            onChange={(e) =>
              setFormData({ ...formData, content: e.target.value })
            }
            placeholder="Enter prompt content..."
            className={`flex-1 min-h-[200px] resize-none ${
              !formValidation.contentValid && formData.content
                ? "border-red-500"
                : ""
            }`}
          />
          {formValidation.contentError && (
            <p className="text-sm text-red-600 mt-1">
              {formValidation.contentError}
            </p>
          )}
          <p className="text-sm text-gray-500 mt-1">
            {formData.content.length} / 10000 characters
          </p>
        </div>
      </div>

      <div className="flex justify-end gap-2">
        <Button variant="outline" onClick={onCancel} disabled={isSaving}>
          Cancel
        </Button>
        <Button onClick={onSave} disabled={!formValidation.canSave || isSaving}>
          {isSaving ? "Saving..." : isCreating ? "Create" : "Update"}
        </Button>
      </div>
    </div>
  );
}

// List component for displaying prompts
interface PromptListProps {
  prompts: Prompt[];
  isLoading: boolean;
  onEdit: (prompt: Prompt) => void;
  onDelete: (prompt: Prompt) => void;
  onSetDefault: (prompt: Prompt) => void;
  onSelect?: (prompt: Prompt) => void;
}

function PromptList({
  prompts,
  isLoading,
  onEdit,
  onDelete,
  onSetDefault,
  onSelect,
}: PromptListProps) {
  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-gray-500">Loading prompts...</div>
      </div>
    );
  }

  if (prompts.length === 0) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <p className="text-gray-500 mb-2">No prompts found</p>
          <p className="text-sm text-gray-400">
            Create your first prompt to get started
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-3 overflow-y-auto h-full">
      {prompts.map((prompt) => (
        <Card key={prompt.id} className="hover:shadow-md transition-shadow">
          <CardContent className="p-4">
            <div className="flex items-start justify-between">
              <div className="flex-1 min-w-0">
                <div className="flex items-center gap-2 mb-2">
                  <h4 className="font-medium truncate">{prompt.name}</h4>
                  {prompt.isDefault && (
                    <Badge variant="secondary" className="text-xs">
                      Default
                    </Badge>
                  )}
                </div>
                <p className="text-sm text-gray-600 line-clamp-2 mb-2">
                  {prompt.content}
                </p>
                <div className="flex items-center gap-4 text-xs text-gray-500">
                  <span>
                    Created: {new Date(prompt.createdAt).toLocaleDateString()}
                  </span>
                  <span>
                    Updated: {new Date(prompt.updatedAt).toLocaleDateString()}
                  </span>
                  <span>{prompt.content.length} characters</span>
                </div>
              </div>

              <div className="flex items-center gap-1 ml-4">
                <TooltipProvider>
                  {onSelect && (
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => onSelect(prompt)}
                        >
                          Select
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>Use this prompt</p>
                      </TooltipContent>
                    </Tooltip>
                  )}

                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => onSetDefault(prompt)}
                        disabled={prompt.isDefault}
                      >
                        {prompt.isDefault ? (
                          <Star className="h-4 w-4 text-yellow-500" />
                        ) : (
                          <StarOff className="h-4 w-4" />
                        )}
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>
                        {prompt.isDefault ? "Default prompt" : "Set as default"}
                      </p>
                    </TooltipContent>
                  </Tooltip>

                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => onEdit(prompt)}
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Edit prompt</p>
                    </TooltipContent>
                  </Tooltip>

                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => onDelete(prompt)}
                        disabled={prompts.length === 1}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>
                        {prompts.length === 1
                          ? "Cannot delete last prompt"
                          : "Delete prompt"}
                      </p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}
